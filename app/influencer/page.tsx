import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import QnAClient from '@/components/influencer/qna/QnAClient';
import { <PERSON><PERSON><PERSON>, HelpCircle, Trophy } from 'lucide-react';
import AnalyticsTab from "@/components/influencer/analytics/AnalyticsTab";

// Page is a server component
export default function InfluencerPage() {
  return (
    <div className="container mx-auto px-1 min-h-screen relative overflow-hidden">
      {/* Clean Premium Background Design */}
      <div className="fixed inset-0 -z-10">
        {/* Main gradient background following influencer color scheme */}
        <div className="absolute inset-0 bg-gradient-to-br from-white via-blue-50/60 to-purple-50/40 dark:from-zinc-950 dark:via-zinc-900 dark:to-zinc-800"></div>

        {/* Purple accent areas - 30% of color scheme */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(196,181,253,0.15),transparent_50%)] dark:bg-[radial-gradient(circle_at_25%_25%,rgba(196,181,253,0.08),transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_75%_75%,rgba(196,181,253,0.12),transparent_50%)] dark:bg-[radial-gradient(circle_at_75%_75%,rgba(196,181,253,0.06),transparent_50%)]"></div>

        {/* Blue accent elements - 10% for CTAs */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_50%_0%,rgba(59,130,246,0.08),transparent_40%)] dark:bg-[radial-gradient(ellipse_at_50%_0%,rgba(59,130,246,0.04),transparent_40%)]"></div>

        {/* Subtle geometric overlay for texture */}
        <div className="absolute inset-0 opacity-[0.03] dark:opacity-[0.015]" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23C4B5FD' fill-opacity='0.4'%3E%3Cpath d='M20 20.5a.5.5 0 1 1 0-1 .5.5 0 0 1 0 1z'/%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>

        {/* Diagonal gradient overlay for depth */}
        <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-purple-100/20 to-blue-100/20 dark:from-transparent dark:via-purple-900/10 dark:to-blue-900/10"></div>
      </div>
      <Tabs defaultValue="analytics" className="w-full flex flex-col h-full relative z-10">
        {/* Fixed tab bar at the top with enhanced premium styling */}
        <div className="sticky top-[50px] left-0 right-0 z-20 bg-white/95 dark:bg-zinc-900/95 backdrop-blur-md pt-2 pb-1 border-b border-[#C4B5FD]/30 dark:border-zinc-700/50 shadow-sm">
          {/* Subtle gradient overlay for tab bar */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#C4B5FD]/5 to-transparent dark:via-[#C4B5FD]/2"></div>

          <TabsList className="grid grid-cols-3 bg-[#C4B5FD]/15 dark:bg-zinc-800/80 p-1 rounded-xl shadow-lg shadow-[#C4B5FD]/10 dark:shadow-zinc-900/20 mx-auto backdrop-blur-sm border border-[#C4B5FD]/20 dark:border-zinc-700/30">
            <TabsTrigger
              value="analytics"
              className="font-medium text-gray-700 dark:text-white transition-all duration-300 rounded-lg
                data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#3B82F6] data-[state=active]:to-[#3B82F6]/90
                data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-blue-500/25
                hover:bg-[#C4B5FD]/10 dark:hover:bg-zinc-700/50"
            >
              <BarChart className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Analytics & News</span>
            </TabsTrigger>
            <TabsTrigger
              value="qna"
              className="font-medium text-gray-700 dark:text-white transition-all duration-300 rounded-lg
                data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#3B82F6] data-[state=active]:to-[#3B82F6]/90
                data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-blue-500/25
                hover:bg-[#C4B5FD]/10 dark:hover:bg-zinc-700/50"
            >
              <HelpCircle className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Q&A</span>
            </TabsTrigger>
            <TabsTrigger
              value="competitions"
              className="font-medium text-gray-700 dark:text-white transition-all duration-300 rounded-lg
                data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#3B82F6] data-[state=active]:to-[#3B82F6]/90
                data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-blue-500/25
                hover:bg-[#C4B5FD]/10 dark:hover:bg-zinc-700/50"
            >
              <Trophy className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Competitions</span>
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Scrollable content area with enhanced premium styling */}
        <div className="flex-1 overflow-y-auto pt-4 relative">
          <TabsContent value="analytics" className="h-full pt-2 flex justify-center">
            <AnalyticsTab />
          </TabsContent>

          <TabsContent value="qna" className="h-full pt-8">
            <Card className="bg-white/95 dark:bg-zinc-800/95 border-[#C4B5FD]/30 dark:border-zinc-700/50 shadow-xl shadow-[#C4B5FD]/5 dark:shadow-zinc-900/20 text-gray-900 dark:text-white backdrop-blur-sm rounded-2xl overflow-hidden">
              {/* Card header gradient */}
              <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-[#C4B5FD]/50 to-transparent"></div>
              <CardContent className="pt-6 relative">
                <QnAClient />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="competitions" className="h-full pt-8">
            <Card className="bg-white/95 dark:bg-zinc-800/95 border-[#C4B5FD]/30 dark:border-zinc-700/50 shadow-xl shadow-[#C4B5FD]/5 dark:shadow-zinc-900/20 text-gray-900 dark:text-white backdrop-blur-sm rounded-2xl overflow-hidden">
              {/* Card header gradient */}
              <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-[#C4B5FD]/50 to-transparent"></div>
              <CardContent className="pt-6 relative">
                <div className="text-center py-12">
                  {/* Premium icon background */}
                  <div className="mx-auto w-20 h-20 bg-gradient-to-br from-[#C4B5FD]/20 to-[#3B82F6]/20 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-[#C4B5FD]/10">
                    <Trophy className="w-10 h-10 text-[#3B82F6]" />
                  </div>
                  <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-[#C4B5FD] via-[#3B82F6] to-[#C4B5FD] bg-clip-text text-transparent">Daily Competitions</h2>
                  <p className="text-gray-600 dark:text-zinc-300 text-lg leading-relaxed max-w-md mx-auto">Stay tuned for exciting daily competitions between influencers!</p>

                  {/* Decorative elements */}
                  <div className="mt-8 flex justify-center space-x-2">
                    <div className="w-2 h-2 bg-[#C4B5FD] rounded-full animate-pulse"></div>
                    <div className="w-2 h-2 bg-[#3B82F6] rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                    <div className="w-2 h-2 bg-[#C4B5FD] rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}