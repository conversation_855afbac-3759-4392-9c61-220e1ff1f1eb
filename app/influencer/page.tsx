import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import QnAClient from '@/components/influencer/qna/QnAClient';
import { <PERSON><PERSON><PERSON>, HelpCircle, Trophy } from 'lucide-react';
import AnalyticsTab from "@/components/influencer/analytics/AnalyticsTab";

// Page is a server component
export default function InfluencerPage() {
  return (
    <div className="container mx-auto px-1 min-h-screen relative overflow-hidden">
      {/* Premium Background Design - Enhanced Visibility */}
      <div className="fixed inset-0 -z-10">
        {/* Base gradient following influencer color scheme - More visible */}
        <div className="absolute inset-0 bg-gradient-to-br from-white via-blue-50/70 to-purple-50/30 dark:from-zinc-950 dark:via-zinc-900 dark:to-zinc-800"></div>

        {/* Enhanced purple accent gradients - 30% of color scheme */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_30%,rgba(196,181,253,0.15),transparent_40%)] dark:bg-[radial-gradient(circle_at_20%_30%,rgba(196,181,253,0.08),transparent_40%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_70%,rgba(196,181,253,0.12),transparent_50%)] dark:bg-[radial-gradient(circle_at_80%_70%,rgba(196,181,253,0.06),transparent_50%)]"></div>

        {/* Enhanced blue accent elements - 10% of color scheme for CTAs */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_60%_20%,rgba(59,130,246,0.08),transparent_60%)] dark:bg-[radial-gradient(ellipse_at_60%_20%,rgba(59,130,246,0.04),transparent_60%)]"></div>

        {/* More visible geometric pattern overlay */}
        <div className="absolute inset-0 opacity-[0.04] dark:opacity-[0.02]" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23C4B5FD' fill-opacity='0.6'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>

        {/* Enhanced animated floating elements for depth */}
        <div className="absolute top-1/4 left-1/6 w-40 h-40 bg-gradient-to-r from-[#C4B5FD]/15 to-[#3B82F6]/15 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '4s' }}></div>
        <div className="absolute bottom-1/3 right-1/5 w-32 h-32 bg-gradient-to-l from-[#3B82F6]/12 to-[#C4B5FD]/12 rounded-full blur-2xl animate-pulse" style={{ animationDuration: '6s', animationDelay: '2s' }}></div>
        <div className="absolute top-2/3 left-3/4 w-28 h-28 bg-gradient-to-br from-[#C4B5FD]/10 to-transparent rounded-full blur-xl animate-pulse" style={{ animationDuration: '5s', animationDelay: '1s' }}></div>

        {/* Enhanced mesh gradient for modern premium look */}
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(196,181,253,0.04)_50%,transparent_75%)] dark:bg-[linear-gradient(45deg,transparent_25%,rgba(196,181,253,0.02)_50%,transparent_75%)]"></div>

        {/* More visible light rays effect */}
        <div className="absolute top-0 left-1/2 w-px h-full bg-gradient-to-b from-[#C4B5FD]/30 via-transparent to-transparent transform -translate-x-1/2 opacity-40 dark:opacity-15"></div>
        <div className="absolute top-0 left-1/3 w-px h-full bg-gradient-to-b from-[#3B82F6]/25 via-transparent to-transparent transform -translate-x-1/2 opacity-30 dark:opacity-10"></div>
        <div className="absolute top-0 left-2/3 w-px h-full bg-gradient-to-b from-[#C4B5FD]/25 via-transparent to-transparent transform -translate-x-1/2 opacity-30 dark:opacity-10"></div>

        {/* Additional premium elements for more visual interest */}
        <div className="absolute inset-0 bg-[conic-gradient(from_0deg_at_50%_50%,transparent_0deg,rgba(196,181,253,0.03)_60deg,transparent_120deg,rgba(59,130,246,0.02)_180deg,transparent_240deg,rgba(196,181,253,0.03)_300deg,transparent_360deg)]"></div>
      </div>
      <Tabs defaultValue="analytics" className="w-full flex flex-col h-full relative z-10">
        {/* Fixed tab bar at the top with enhanced premium styling */}
        <div className="sticky top-[50px] left-0 right-0 z-20 bg-white/95 dark:bg-zinc-900/95 backdrop-blur-md pt-2 pb-1 border-b border-[#C4B5FD]/30 dark:border-zinc-700/50 shadow-sm">
          {/* Subtle gradient overlay for tab bar */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#C4B5FD]/5 to-transparent dark:via-[#C4B5FD]/2"></div>

          <TabsList className="grid grid-cols-3 bg-[#C4B5FD]/15 dark:bg-zinc-800/80 p-1 rounded-xl shadow-lg shadow-[#C4B5FD]/10 dark:shadow-zinc-900/20 mx-auto backdrop-blur-sm border border-[#C4B5FD]/20 dark:border-zinc-700/30">
            <TabsTrigger
              value="analytics"
              className="font-medium text-gray-700 dark:text-white transition-all duration-300 rounded-lg
                data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#3B82F6] data-[state=active]:to-[#3B82F6]/90
                data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-blue-500/25
                hover:bg-[#C4B5FD]/10 dark:hover:bg-zinc-700/50"
            >
              <BarChart className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Analytics & News</span>
            </TabsTrigger>
            <TabsTrigger
              value="qna"
              className="font-medium text-gray-700 dark:text-white transition-all duration-300 rounded-lg
                data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#3B82F6] data-[state=active]:to-[#3B82F6]/90
                data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-blue-500/25
                hover:bg-[#C4B5FD]/10 dark:hover:bg-zinc-700/50"
            >
              <HelpCircle className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Q&A</span>
            </TabsTrigger>
            <TabsTrigger
              value="competitions"
              className="font-medium text-gray-700 dark:text-white transition-all duration-300 rounded-lg
                data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#3B82F6] data-[state=active]:to-[#3B82F6]/90
                data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-blue-500/25
                hover:bg-[#C4B5FD]/10 dark:hover:bg-zinc-700/50"
            >
              <Trophy className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Competitions</span>
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Scrollable content area with enhanced premium styling */}
        <div className="flex-1 overflow-y-auto pt-4 relative">
          <TabsContent value="analytics" className="h-full pt-2 flex justify-center">
            <AnalyticsTab />
          </TabsContent>

          <TabsContent value="qna" className="h-full pt-8">
            <Card className="bg-white/95 dark:bg-zinc-800/95 border-[#C4B5FD]/30 dark:border-zinc-700/50 shadow-xl shadow-[#C4B5FD]/5 dark:shadow-zinc-900/20 text-gray-900 dark:text-white backdrop-blur-sm rounded-2xl overflow-hidden">
              {/* Card header gradient */}
              <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-[#C4B5FD]/50 to-transparent"></div>
              <CardContent className="pt-6 relative">
                <QnAClient />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="competitions" className="h-full pt-8">
            <Card className="bg-white/95 dark:bg-zinc-800/95 border-[#C4B5FD]/30 dark:border-zinc-700/50 shadow-xl shadow-[#C4B5FD]/5 dark:shadow-zinc-900/20 text-gray-900 dark:text-white backdrop-blur-sm rounded-2xl overflow-hidden">
              {/* Card header gradient */}
              <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-[#C4B5FD]/50 to-transparent"></div>
              <CardContent className="pt-6 relative">
                <div className="text-center py-12">
                  {/* Premium icon background */}
                  <div className="mx-auto w-20 h-20 bg-gradient-to-br from-[#C4B5FD]/20 to-[#3B82F6]/20 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-[#C4B5FD]/10">
                    <Trophy className="w-10 h-10 text-[#3B82F6]" />
                  </div>
                  <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-[#C4B5FD] via-[#3B82F6] to-[#C4B5FD] bg-clip-text text-transparent">Daily Competitions</h2>
                  <p className="text-gray-600 dark:text-zinc-300 text-lg leading-relaxed max-w-md mx-auto">Stay tuned for exciting daily competitions between influencers!</p>

                  {/* Decorative elements */}
                  <div className="mt-8 flex justify-center space-x-2">
                    <div className="w-2 h-2 bg-[#C4B5FD] rounded-full animate-pulse"></div>
                    <div className="w-2 h-2 bg-[#3B82F6] rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                    <div className="w-2 h-2 bg-[#C4B5FD] rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}