@tailwind base;
@tailwind components;
@tailwind utilities;


.roboto {
  font-family: var(--font-roboto);
}

.poppins {
  font-family: var(--font-poppins);
}
.RubikMonoOne {
  font-family: var(--font-rubik);
}
.kanit {
  font-family: var(--font-kanit);
}
.cambay {
  font-family: var(--font-cambay);
}

/* Safe area handling for mobile devices with notches - limited to bottom only */
:root {
  --safe-area-right: env(safe-area-inset-right);
  --safe-area-bottom: env(safe-area-inset-bottom);
  --safe-area-left: env(safe-area-inset-left);
}

/* Apply safe area padding to fixed elements - removed top padding */
.safe-right {
  padding-right: var(--safe-area-right);
}

.safe-bottom {
  padding-bottom: var(--safe-area-bottom);
}

.safe-left {
  padding-left: var(--safe-area-left);
}

/* Only apply side and bottom padding */
.safe-area-padding {
  padding-right: var(--safe-area-right);
  padding-bottom: var(--safe-area-bottom);
  padding-left: var(--safe-area-left);
}

/* Responsive status bar height adjustment - TOP PADDING REMOVED */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .has-safe-area {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Adjustments for standalone mode (PWA) - TOP PADDING REMOVED */
@media screen and (display-mode: standalone) {
  body {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .pwa-standalone-padding {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* @layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
} */


/* @import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap'); */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83% 53.9%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83% 53.9%;

    --radius: 1rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
    /* COMPLETELY disable text scaling - ignore device font size settings */
    -webkit-text-size-adjust: none !important;
    -moz-text-size-adjust: none !important;
    -ms-text-size-adjust: none !important;
    text-size-adjust: none !important;
    /* Force consistent font rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Set fixed font size that ignores system settings */
    font-size: 16px !important;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    overflow-x: hidden;
    /* COMPLETELY disable text scaling - ignore device font size settings */
    -webkit-text-size-adjust: none !important;
    text-size-adjust: none !important;
    /* Fixed font size that ignores system settings */
    font-size: 16px !important;
    /* Consistent line height across devices */
    line-height: 1.5;
    /* Prevent font boosting */
    max-height: 999999px;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold tracking-tight;
    /* COMPLETELY disable text scaling on headings */
    -webkit-text-size-adjust: none !important;
    text-size-adjust: none !important;
    max-height: 999999px;
  }

  /* Mobile-specific font consistency - COMPLETELY IGNORE DEVICE FONT SIZE */
  @media screen and (max-width: 768px) {
    * {
      /* COMPLETELY disable automatic font scaling */
      -webkit-text-size-adjust: none !important;
      text-size-adjust: none !important;
      /* Consistent font rendering */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    /* Force fixed font sizes on form inputs (iOS) */
    input, textarea, select {
      font-size: 16px !important;
      -webkit-text-size-adjust: none !important;
      text-size-adjust: none !important;
    }
  }

  /* High DPI display adjustments */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    body {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  }

  /* Force FIXED text sizing - IGNORE device font size settings completely */
  @supports (-webkit-touch-callout: none) {
    /* iOS Safari specific - DISABLE text size adjustment */
    * {
      -webkit-text-size-adjust: none !important;
      text-size-adjust: none !important;
    }
  }

  /* Android Chrome specific - DISABLE text size adjustment */
  @media screen and (-webkit-device-pixel-ratio: 1) {
    * {
      -webkit-text-size-adjust: none !important;
      text-size-adjust: none !important;
    }
  }

  /* Prevent font boosting and FORCE fixed sizing on all elements */
  *, *::before, *::after {
    max-height: 999999px;
    -webkit-text-size-adjust: none !important;
    text-size-adjust: none !important;
  }

  /* Additional override for any remaining text scaling */
  html, body, div, span, p, a, h1, h2, h3, h4, h5, h6,
  button, input, textarea, select, label {
    -webkit-text-size-adjust: none !important;
    text-size-adjust: none !important;
  }

  .glass {
    @apply bg-white/70 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-black/40 backdrop-blur-md border border-white/10;
  }

  .preloader-active {
    overflow: hidden;
  }

  .text-balance {
    text-wrap: balance;
  }

  .section-padding {
    @apply px-6 py-20 md:px-10 md:py-28 lg:px-20;
  }

  .container-custom {
    @apply w-full max-w-[1400px] mx-auto px-6 md:px-8 lg:px-12;
  }

  /* Mobile-friendly toast styling */
  .mobile-toast {
    @apply max-w-[90vw] sm:max-w-[420px] text-sm;
  }

  .mobile-toast .toast-title {
    @apply text-sm font-medium;
  }

  .mobile-toast .toast-description {
    @apply text-xs opacity-90;
  }
}

/* Animations and transitions */
@layer utilities {
  .transition-all-300 {
    @apply transition-all duration-300 ease-in-out;
  }

  .transition-transform-300 {
    @apply transition-transform duration-300 ease-in-out;
  }

  .transition-opacity-300 {
    @apply transition-opacity duration-300 ease-in-out;
  }

  .animate-delay-100 {
    animation-delay: 100ms;
  }

  .animate-delay-200 {
    animation-delay: 200ms;
  }

  .animate-delay-300 {
    animation-delay: 300ms;
  }

  .animate-delay-400 {
    animation-delay: 400ms;
  }

  .animate-delay-500 {
    animation-delay: 500ms;
  }

  .animate-delay-600 {
    animation-delay: 600ms;
  }

  .animate-delay-700 {
    animation-delay: 700ms;
  }

  .animate-delay-800 {
    animation-delay: 800ms;
  }

  .animate-delay-900 {
    animation-delay: 900ms;
  }

  .animate-delay-1000 {
    animation-delay: 1000ms;
  }

  .transform-center {
    transform-origin: center;
  }
}

/* New animations and styles for the influencer feed page */
@keyframes gradient-xy {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}

.animate-gradient-xy {
  background-size: 200% 200%;
  animation: gradient-xy 15s ease infinite;
}

.animation-delay-150 {
  animation-delay: 150ms;
}

/* Paper texture background for posts */
.bg-texture-paper {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

/* Add slide-up animation for notification prompt */
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out forwards;
}

/* Added animation for profile page blob elements */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -20px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 10px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

/* Added floating animation for particles */
@keyframes float {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 0.5;
  }
  25% {
    transform: translateY(-10px) translateX(5px);
    opacity: 0.8;
  }
  50% {
    transform: translateY(0) translateX(10px);
    opacity: 0.6;
  }
  75% {
    transform: translateY(10px) translateX(5px);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0) translateX(0);
    opacity: 0.5;
  }
}

/* Added shimmer animation for Instagram card highlights */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Added premium gradient shift animation (background-position style) */
@keyframes premiumGradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Slow gradient animation for profile header */
@keyframes gradient-slow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Post card glow animation */
@keyframes postCardGlow {
  0% {
    box-shadow: 0 0 15px rgba(192, 38, 211, 0.3);
    border-color: rgba(192, 38, 211, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(192, 38, 211, 0.5);
    border-color: rgba(192, 38, 211, 0.5);
  }
  100% {
    box-shadow: 0 0 15px rgba(192, 38, 211, 0.3);
    border-color: rgba(192, 38, 211, 0.3);
  }
}

.animate-shimmer {
  background: linear-gradient(90deg,
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,0.2) 25%,
    rgba(255,255,255,0.2) 50%,
    rgba(255,255,255,0) 100%);
  background-size: 200% 100%;
  animation: shimmer 3s infinite;
  color: transparent;
}

.animate-float {
  animation: float 15s ease-in-out infinite;
}

.animate-blob {
  animation: blob 15s infinite alternate cubic-bezier(0.7, 0.2, 0.4, 0.8);
}

.animate-post-glow {
  animation: postCardGlow 3s ease-in-out infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-3000 {
  animation-delay: 3s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.animation-delay-6000 {
  animation-delay: 6s;
}

/* Instagram UI specific utilities */
.bg-gradient-instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.text-gradient-instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

/* Glass card hover effects */
.hover-glass:hover {
  @apply backdrop-blur-lg bg-white/80 dark:bg-slate-800/80 shadow-lg;
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* Chat media styling */
.media-sent .rounded-lg {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.media-sent audio,
.media-sent video,
.media-sent img {
  border-radius: 0.75rem;
}

/* Fix for image display in chat */
.media-sent img {
  background-color: transparent !important;
}

/* Fix for audio player in chat */
.media-sent .bg-transparent {
  background-color: rgba(79, 70, 229, 0.15) !important;
  border-radius: 1rem;
}

/* Apply premium gradient shift animation */
.animate-premium-gradient {
  /* Define the large gradient for the animation */
  background: linear-gradient(
    135deg,
    #1e1b4b, /* Deep Indigo */
    #4c1d95, /* Violet */
    #6d28d9, /* Purple */
    #86198f, /* Fuchsia */
    #1e3a8a, /* Dark Blue */
    #6d28d9, /* Purple */
    #4c1d95, /* Violet */
    #1e1b4b  /* Deep Indigo */
  );
  background-size: 400% 400%; /* Make the background larger than the element */
  animation: premiumGradientShift 15s ease infinite; /* Apply the shifting animation */
}

.animate-gradient-slow {
  background-size: 200% 200%;
  animation: gradient-slow 8s ease infinite;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Hide horizontal scrollbar for influencer cards */
.hide-scrollbar::-webkit-scrollbar { display: none; }
.hide-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
}

/* Enhanced gradient background for deals pages */
.subtle-gradient-bg {
  /* Light mode: very subtle white gradient */
  background: linear-gradient(135deg,
    #ffffff 0%, #f8f9fa 50%, #ffffff 100%
  );
}

.dark .subtle-gradient-bg {
  /* Dark mode: very subtle black gradient with minimal difference */
  background: linear-gradient(135deg,
    #0a0a0a 0%,
    #0d0d0d 50%,
    #0a0a0a 100%
  );
}

/* Premium animations for influencer page */
@keyframes float-gentle {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(1deg);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes shimmer-subtle {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Utility classes for premium effects */
.animate-float-gentle {
  animation: float-gentle 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 4s ease-in-out infinite;
}

.animate-shimmer-subtle {
  animation: shimmer-subtle 3s ease-in-out infinite;
}

.animate-gradient-shift {
  animation: gradient-shift 8s ease-in-out infinite;
  background-size: 200% 200%;
}
